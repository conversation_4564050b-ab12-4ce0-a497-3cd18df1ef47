#!/bin/bash

mkfile_path := $(abspath $(lastword $(MAKEFILE_LIST)))
current_dir := $(notdir $(patsubst %/,%,$(dir $(mkfile_path))))
EXE= $(current_dir:.d=)

DEFINES=  -D TB -D WINDOWS -D W32_BUILD
CC=C:/msys64/mingw64/bin/g++.exe

OBJECTS=src/hawkeye.cpp src/tbprobe.cpp

CXXFLAGS= $(DEFINES) -m64 -mpopcnt -mbmi2 -pipe -fomit-frame-pointer -Ofast -Wno-array-bounds -march=native -msse4.2 --param max-inline-insns-auto=600 --param inline-min-speedup=5 -funsafe-loop-optimizations -pthread -fprofile-generate

CXXFLAGS-R= $(DEFINES) -m64 -mpopcnt -mbmi2 -pipe -fomit-frame-pointer -flto -Ofast -march=native -msse4.2 --param max-inline-insns-auto=100 --param inline-min-speedup=25 -funsafe-loop-optimizations -Wno-coverage-mismatch -pthread -fprofile-use -fprofile-correction

STRIP=C:/msys64/mingw64/bin/strip.exe

clean:
	-del /Q src\*.o *.gcda *.gcno 2>nul

build:
	-del /Q src\*.o 2>nul
	$(CC) $(DEFINES) -m64 -mpopcnt -mbmi2 -O3 -flto -march=native $(OBJECTS) -o $(EXE).exe

build-fast:
	-del /Q src\*.o 2>nul
	$(CC) $(DEFINES) -m64 -mpopcnt -mbmi2 -Ofast -flto -march=native -funroll-loops -ffast-math $(OBJECTS) -o $(EXE).exe

build-static:
	-del /Q src\*.o 2>nul
	$(CC) $(DEFINES) -m64 -mpopcnt -mbmi2 -O3 -flto -march=native -static -static-libgcc -static-libstdc++ $(OBJECTS) -o $(EXE)-static.exe

build-fritz:
	-del /Q src\*.o 2>nul
	$(CC) $(DEFINES) -m64 -mpopcnt -O2 -static -static-libgcc -static-libstdc++ -Wl,--strip-all $(OBJECTS) -o $(EXE)-fritz.exe

build-compatible:
	-del /Q src\*.o 2>nul
	$(CC) $(DEFINES) -m64 -O2 -static -static-libgcc -static-libstdc++ -Wl,--strip-all $(OBJECTS) -o $(EXE)-compatible.exe

g-pro:
	-del /Q src\*.o *.gcda *.gcno 2>nul
	$(CC) $(CXXFLAGS) $(OBJECTS) -o $(EXE).exe

g-rel:
	-del /Q src\*.o 2>nul
	$(CC) $(CXXFLAGS-R) $(OBJECTS) -o $(EXE).exe
	$(STRIP) $(EXE).exe



## to build PGO Build for the Mac:
## have the makefile above the source direcotry
## move the files in the syzygy folder to the src folder
## e.g., path might be /sources/hawkeye/hawkeye-1.01.d/src
## makefile will name the exe "hawkeye-1.01."
## adjust defines/flags as neccessary , you might want to change CC to gcc ,i.e, "CC=gcc" w/o quotes
## type "g-pro" w/o quotes
## start hawkeye at the command prompt
## go depth 16
## quit
## type "g-rel" w/o quotes
## take out the upx2 line if you don't have upx2 ( exe compressor)
## please do not ask me how to compile for windows or linux - not my thing...
## I left the old gull makefiles intact, they may be usefull, I'm not sure




