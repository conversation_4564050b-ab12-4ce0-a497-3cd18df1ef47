# Hawkeye
UCI Chess Engine based on Gull 3-syzygy
=====================

This is a Mac version the Hawkeye chess engine by <PERSON> based on 
Gull version 3.

License
-------

The original Gull 3 source code by <PERSON><PERSON><PERSON> is in the "public domain".  The modified Gull3 syzygy source/exe was
released under the MIT License by <PERSON> as is this software.


The syzygy code ( all files in the syzygy folder) was authored  by <PERSON> (c) 2011-2016.
These files may be redistributed and/or modified without restrictions.

Currently the second strongest open source chess engine (Gull/Hawkeye) available for the Mac behind Stockfish.

Plays very strong chess at time control of  75 sec base with 1.50 sec increment:

(excerpt from tourney)

1520 of 3800 games completed...time control: 7500+150

Date: 07/10/16 : 12:49:18
Rank Name                       Elo    +    - Games   Score    
   1 Stockfish 070916-x        3497   51   51   152   125.0   
   2 SugaR 2.5, a derivative   3487   50   50   152   125.5   
   3 Komodo 10 64-bit          3471   49   49   152   123.5   
   4 Stockfish 7 64 POPCNT     3430   47   47   152   118.0   
   5 Stockfish DD 64 SSE4.2    3362   44   44   152   107.0   
   6 Hawkeye 1.01 OSx64        3359   44   44   152   105.0   
   7 Gull 3 x64 (syzygy)       3295   43   43   152    94.0   
   8 Critter 1.6a 64-bit       3248   43   43   152    85.5   
   9 Firenzina 2.4.4 xTreme L  3194   42   42   152    75.0   
  10 Protector 1.9.0           3188   43   43   152    73.5   
  11 Mikhail 0.04 OSx 64       3171   43   43   152    70.5   
  12 Hakkapeliitta TCEC v2     3139   44   44   152    64.5   
  13 Texel 1.05 64-bit         3094   44   44   152    55.5   
  14 Kohai 1.0                 3091   44   44   152    55.0   
  15 Senpai 1.0                3081   44   44   152    54.0
